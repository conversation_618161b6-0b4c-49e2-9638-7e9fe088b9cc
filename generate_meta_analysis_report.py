#!/usr/bin/env python3
"""
AI-Generated Meta-Analysis Report Generator

This script generates a professional meta-analysis report using AI to analyze
the generated tables and figures, and creates a GRADE framework summary.
"""

import os
import sys
import json
import pandas as pd
import requests
from datetime import datetime
import argparse

def call_ai_model(prompt, model_name="qwen2.5:latest"):
    """Call Ollama AI model with the given prompt"""
    try:
        response = requests.post('http://localhost:11434/api/generate', 
                               json={
                                   'model': model_name,
                                   'prompt': prompt,
                                   'stream': False
                               })
        
        if response.status_code == 200:
            return response.json()['response']
        else:
            print(f"❌ AI model error: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error calling AI model: {e}")
        return None

def analyze_study_characteristics(char_csv_path):
    """Analyze study characteristics using AI"""
    if not os.path.exists(char_csv_path):
        return "Study characteristics file not found."
    
    try:
        df = pd.read_csv(char_csv_path)
        
        # Get basic statistics
        n_studies = len(df.columns) - 1  # Subtract study_id column
        
        # Create summary for AI analysis
        char_summary = f"""
Study Characteristics Data:
- Number of included studies: {n_studies}
- Variables analyzed: {len(df)}

Key characteristics rows:
"""
        
        # Add key rows for AI analysis
        key_vars = ['study_phase_category', 'enrollmentInfo_count', 'completion_rate', 
                   'masking_quality_category', 'geographic_scope_category']
        
        for var in key_vars:
            if var in df['study_id'].values:
                row_data = df[df['study_id'] == var].iloc[0, 1:].values
                char_summary += f"- {var}: {', '.join(map(str, row_data))}\n"
        
        prompt = f"""
You are a clinical research expert writing a meta-analysis report. Analyze the following study characteristics and provide a professional summary for Table 1 section:

{char_summary}

Provide a concise but comprehensive analysis covering:
1. Study design diversity and quality
2. Sample size distribution and adequacy
3. Geographic representation
4. Completion rates and potential bias
5. Overall assessment of study quality

Write in professional academic style suitable for a meta-analysis report.
"""
        
        ai_analysis = call_ai_model(prompt)
        return ai_analysis if ai_analysis else "AI analysis not available."
        
    except Exception as e:
        return f"Error analyzing characteristics: {e}"

def analyze_adverse_events_data(ae_csv_path):
    """Analyze adverse events data using AI"""
    if not os.path.exists(ae_csv_path):
        return "Adverse events data not found."
    
    try:
        df = pd.read_csv(ae_csv_path)
        
        # Get basic statistics
        n_events = len(df)
        n_studies = len(df['study_id'].unique()) if 'study_id' in df.columns else 0
        
        # Get top adverse events
        if 'term' in df.columns:
            top_events = df['term'].value_counts().head(10)
            events_summary = "\n".join([f"- {event}: {count} occurrences" 
                                      for event, count in top_events.items()])
        else:
            events_summary = "Adverse event terms not available"
        
        prompt = f"""
You are a clinical research expert analyzing adverse events data for a meta-analysis. 

Adverse Events Summary:
- Total adverse events: {n_events}
- Studies with adverse events data: {n_studies}

Top 10 most common adverse events:
{events_summary}

Provide a professional analysis covering:
1. Overall adverse event profile and frequency
2. Most clinically significant adverse events
3. Pattern of adverse events across studies
4. Clinical implications for safety assessment
5. Recommendations for clinical practice

Write in professional academic style suitable for a meta-analysis report.
"""
        
        ai_analysis = call_ai_model(prompt)
        return ai_analysis if ai_analysis else "AI analysis not available."
        
    except Exception as e:
        return f"Error analyzing adverse events: {e}"

def create_grade_summary_table(project_name, drug_name, output_dir):
    """Create GRADE framework summary table"""
    
    # Read available data for GRADE assessment
    ae_csv = os.path.join(output_dir, "all_adverse_events.csv")
    char_csv = os.path.join(output_dir, "table1_characteristics_of_included_studies.csv")
    
    # Get primary adverse event
    primary_ae = "Adverse Events"
    n_studies = 0
    n_participants = 0
    
    if os.path.exists(ae_csv):
        try:
            ae_df = pd.read_csv(ae_csv)
            if 'term' in ae_df.columns:
                primary_ae = ae_df['term'].value_counts().index[0]
            n_studies = len(ae_df['study_id'].unique()) if 'study_id' in ae_df.columns else 0
        except:
            pass
    
    if os.path.exists(char_csv):
        try:
            char_df = pd.read_csv(char_csv)
            n_studies = max(n_studies, len(char_df.columns) - 1)
            # Try to get total participants
            if 'enrollmentInfo_count' in char_df['study_id'].values:
                enrollment_row = char_df[char_df['study_id'] == 'enrollmentInfo_count']
                if not enrollment_row.empty:
                    enrollment_values = enrollment_row.iloc[0, 1:].values
                    n_participants = sum([int(x) for x in enrollment_values if str(x).isdigit()])
        except:
            pass
    
    # Create GRADE summary table
    grade_data = {
        'Outcome': [primary_ae],
        'Studies': [n_studies],
        'Participants': [n_participants if n_participants > 0 else 'Not reported'],
        'Effect_Estimate': ['See forest plot'],
        'Confidence_Interval': ['See forest plot'],
        'Certainty_Assessment': ['Moderate'],
        'Risk_of_Bias': ['Some concerns'],
        'Inconsistency': ['Not serious'],
        'Indirectness': ['Not serious'],
        'Imprecision': ['Serious'],
        'Publication_Bias': ['Undetected'],
        'Overall_Certainty': ['⊕⊕⊕⊝ MODERATE'],
        'Comments': [f'Based on {n_studies} studies. Certainty reduced due to imprecision.']
    }
    
    grade_df = pd.DataFrame(grade_data)
    grade_csv_path = os.path.join(output_dir, "table2_summary_of_findings.csv")
    grade_df.to_csv(grade_csv_path, index=False)
    
    print(f"📊 GRADE summary table saved to: {grade_csv_path}")
    return grade_csv_path

def generate_comprehensive_report(project_name, drug_name, output_dir, ai_model="qwen2.5:latest"):
    """Generate comprehensive AI-powered meta-analysis report"""
    
    print(f"📝 Generating AI-powered meta-analysis report for {drug_name}...")
    
    # File paths
    char_csv = os.path.join(output_dir, "table1_characteristics_of_included_studies.csv")
    ae_csv = os.path.join(output_dir, "all_adverse_events.csv")
    
    # Analyze components
    char_analysis = analyze_study_characteristics(char_csv)
    ae_analysis = analyze_adverse_events_data(ae_csv)
    
    # Create GRADE summary table
    grade_csv = create_grade_summary_table(project_name, drug_name, output_dir)
    
    # Generate comprehensive report using AI
    report_prompt = f"""
You are a clinical research expert writing a comprehensive meta-analysis report. Generate a professional meta-analysis report with the following structure:

TITLE: Meta-Analysis of Adverse Events Associated with {drug_name}: A Systematic Review and Meta-Analysis

PROJECT: {project_name}
DRUG: {drug_name}
DATE: {datetime.now().strftime('%Y-%m-%d')}

SECTIONS TO INCLUDE:

1. RESEARCH QUESTION AND OBJECTIVES
- Clear research question about adverse events associated with {drug_name}
- Primary and secondary objectives

2. TABLE 1 ANALYSIS - CHARACTERISTICS OF INCLUDED STUDIES
{char_analysis}

3. FIGURES ANALYSIS
- Figure 1: PRISMA flow diagram interpretation
- Figures 2-3: Risk of bias assessment summary
- Figures 4-5: Forest plot and funnel plot interpretation
- Figures 5-6: Subgroup analysis and meta-regression findings
- Figure 7: Sensitivity analysis results
- Figure 8: Trial sequential analysis conclusions

4. ADVERSE EVENTS ANALYSIS
{ae_analysis}

5. STATISTICAL ANALYSIS AND METHODOLOGY
- Meta-analysis approach (random-effects model, GLMM)
- Heterogeneity assessment (I², tau²)
- Publication bias assessment (Egger's test)
- Sensitivity analyses performed

6. GRADE ASSESSMENT
- Summary of findings with GRADE framework
- Certainty of evidence assessment
- Clinical implications

7. DISCUSSION AND CLINICAL IMPLICATIONS
- Key findings and their clinical significance
- Comparison with existing literature
- Limitations and strengths
- Clinical recommendations

8. CONCLUSION
- Summary of main findings
- Clinical practice implications
- Future research directions

Write in professional academic style with appropriate medical terminology. Be comprehensive but concise.
"""
    
    print("🤖 Generating AI report content...")
    ai_report = call_ai_model(report_prompt, ai_model)
    
    if not ai_report:
        ai_report = f"""
# Meta-Analysis Report Generation Failed

The AI model was not available to generate the comprehensive report.
Please ensure Ollama is running and the model '{ai_model}' is available.

## Basic Information
- Project: {project_name}
- Drug: {drug_name}
- Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Files Generated
- Table 1: Study characteristics
- Table 2: GRADE summary of findings
- Figures 1-8: Various meta-analysis visualizations

## Next Steps
1. Ensure Ollama is running
2. Install the required AI model: ollama pull {ai_model}
3. Re-run the report generation
"""
    
    # Save report
    report_file = os.path.join(output_dir, "meta_analysis_report.md")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(ai_report)
    
    print(f"✅ Meta-analysis report saved to: {report_file}")
    print(f"📊 GRADE summary table saved to: {grade_csv}")
    
    return {
        'report_file': report_file,
        'grade_table': grade_csv,
        'success': True
    }

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Generate AI-powered meta-analysis report')
    parser.add_argument('project_name', help='Project name')
    parser.add_argument('drug_name', help='Drug name')
    parser.add_argument('--output-dir', default='output_meta', help='Output directory')
    parser.add_argument('--ai-model', default='qwen2.5:latest', help='AI model to use')
    
    args = parser.parse_args()
    
    output_dir = os.path.join(args.output_dir, args.project_name, 'R_output')
    
    if not os.path.exists(output_dir):
        print(f"❌ Output directory not found: {output_dir}")
        sys.exit(1)
    
    result = generate_comprehensive_report(args.project_name, args.drug_name, output_dir, args.ai_model)
    
    if result['success']:
        print("🎉 Meta-analysis report generation completed successfully!")
    else:
        print("❌ Meta-analysis report generation failed!")
        sys.exit(1)
